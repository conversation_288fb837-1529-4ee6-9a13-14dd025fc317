# Generated by Django 5.1.6 on 2025-03-19 16:33

import datetime
import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dispatcharr_channels', '0004_stream_is_custom'),
        ('m3u', '0003_create_custom_account'),
    ]

    operations = [
        migrations.AddField(
            model_name='stream',
            name='channel_group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='streams', to='dispatcharr_channels.channelgroup'),
        ),
        migrations.AddField(
            model_name='stream',
            name='last_seen',
            field=models.DateTimeField(db_index=True, default=datetime.datetime.now),
        ),
        migrations.AlterField(
            model_name='channel',
            name='uuid',
            field=models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, unique=True),
        ),
        migrations.CreateModel(
            name='ChannelGroupM3UAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enabled', models.BooleanField(default=True)),
                ('channel_group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='m3u_account', to='dispatcharr_channels.channelgroup')),
                ('m3u_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='channel_group', to='m3u.m3uaccount')),
            ],
            options={
                'unique_together': {('channel_group', 'm3u_account')},
            },
        ),
    ]
