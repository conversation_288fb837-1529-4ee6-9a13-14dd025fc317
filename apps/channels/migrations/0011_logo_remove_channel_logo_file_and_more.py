# Generated by Django 5.1.6 on 2025-04-01 22:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dispatcharr_channels', '0010_stream_custom_properties'),
    ]

    operations = [
        migrations.CreateModel(
            name='Logo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('url', models.URLField(unique=True)),
            ],
        ),
        migrations.RemoveField(
            model_name='channel',
            name='logo_file',
        ),
        migrations.RemoveField(
            model_name='channel',
            name='logo_url',
        ),
        migrations.AddField(
            model_name='channel',
            name='logo',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='channels', to='dispatcharr_channels.logo'),
        ),
    ]
