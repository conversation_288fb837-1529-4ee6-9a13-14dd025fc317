# Generated by Django 5.1.6 on 2025-03-19 18:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dispatcharr_channels', '0007_remove_stream_group_name'),
    ]

    operations = [
        migrations.AddField(
            model_name='stream',
            name='stream_hash',
            field=models.CharField(db_index=True, help_text='Unique hash for this stream from the M3U account', max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='stream',
            name='logo_url',
            field=models.TextField(blank=True, null=True),
        ),
    ]
