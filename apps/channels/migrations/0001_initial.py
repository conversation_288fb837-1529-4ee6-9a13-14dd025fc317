# Generated by Django 5.1.6 on 2025-03-05 22:07

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
        ('m3u', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChannelGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Channel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('channel_number', models.IntegerField()),
                ('channel_name', models.CharField(max_length=255)),
                ('logo_url', models.URLField(blank=True, max_length=2000, null=True)),
                ('logo_file', models.ImageField(blank=True, null=True, upload_to='logos/')),
                ('tvg_id', models.CharField(blank=True, max_length=255, null=True)),
                ('tvg_name', models.CharField(blank=True, max_length=255, null=True)),
                ('stream_profile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='channels', to='core.streamprofile')),
                ('channel_group', models.ForeignKey(blank=True, help_text='Channel group this channel belongs to.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='channels', to='dispatcharr_channels.channelgroup')),
            ],
        ),
        migrations.CreateModel(
            name='Stream',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='Default Stream', max_length=255)),
                ('url', models.URLField()),
                ('custom_url', models.URLField(blank=True, max_length=2000, null=True)),
                ('logo_url', models.URLField(blank=True, max_length=2000, null=True)),
                ('tvg_id', models.CharField(blank=True, max_length=255, null=True)),
                ('local_file', models.FileField(blank=True, null=True, upload_to='uploads/')),
                ('current_viewers', models.PositiveIntegerField(default=0)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('group_name', models.CharField(blank=True, max_length=255, null=True)),
                ('m3u_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='streams', to='m3u.m3uaccount')),
                ('stream_profile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='streams', to='core.streamprofile')),
            ],
            options={
                'verbose_name': 'Stream',
                'verbose_name_plural': 'Streams',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='ChannelStream',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.PositiveIntegerField(default=0)),
                ('channel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dispatcharr_channels.channel')),
                ('stream', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dispatcharr_channels.stream')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.AddField(
            model_name='channel',
            name='streams',
            field=models.ManyToManyField(blank=True, related_name='channels', through='dispatcharr_channels.ChannelStream', to='dispatcharr_channels.stream'),
        ),
    ]
