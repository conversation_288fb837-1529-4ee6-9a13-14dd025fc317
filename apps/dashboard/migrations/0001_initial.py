# Generated by Django 5.1.6 on 2025-03-05 22:07

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Settings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('server_name', models.Char<PERSON>ield(default='Dispatcharr', max_length=255)),
                ('time_zone', models.CharField(default='UTC', max_length=50)),
                ('default_logo_url', models.URLField(blank=True, null=True)),
                ('max_concurrent_streams', models.PositiveIntegerField(default=10)),
                ('auto_backup_frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly')], default='weekly', max_length=50)),
                ('enable_debug_logs', models.Bo<PERSON>anField(default=False)),
                ('schedules_direct_username', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('schedules_direct_password', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('schedules_direct_update_frequency', models.CharField(choices=[('12h', 'Every 12 Hours'), ('daily', 'Daily')], default='daily', max_length=50)),
                ('schedules_direct_api_key', models.CharField(blank=True, max_length=255, null=True)),
                ('transcoding_bitrate', models.PositiveIntegerField(default=2000)),
                ('transcoding_audio_codec', models.CharField(choices=[('aac', 'AAC'), ('mp3', 'MP3')], default='aac', max_length=50)),
                ('transcoding_resolution', models.CharField(choices=[('720p', '720p'), ('1080p', '1080p')], default='1080p', max_length=50)),
                ('failover_behavior', models.CharField(choices=[('sequential', 'Sequential'), ('random', 'Random')], default='sequential', max_length=50)),
                ('stream_health_check_frequency', models.PositiveIntegerField(default=5)),
                ('email_notifications', models.BooleanField(default=False)),
                ('webhook_url', models.URLField(blank=True, null=True)),
                ('cpu_alert_threshold', models.PositiveIntegerField(default=90)),
                ('memory_alert_threshold', models.PositiveIntegerField(default=90)),
                ('hdhr_integration', models.BooleanField(default=True)),
                ('custom_api_endpoints', models.JSONField(blank=True, null=True)),
                ('backup_path', models.CharField(default='backups/', max_length=255)),
                ('backup_frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly')], default='weekly', max_length=50)),
                ('ffmpeg_path', models.CharField(default='/usr/bin/ffmpeg', max_length=255)),
                ('custom_transcoding_flags', models.TextField(blank=True, null=True)),
                ('celery_worker_concurrency', models.PositiveIntegerField(default=4)),
            ],
        ),
    ]
