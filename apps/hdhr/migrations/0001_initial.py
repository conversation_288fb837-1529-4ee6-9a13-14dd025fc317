# Generated by Django 5.1.6 on 2025-03-05 22:07

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='HDHRDevice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('friendly_name', models.CharField(default='Dispatcharr HDHomeRun', max_length=100)),
                ('device_id', models.CharField(max_length=32, unique=True)),
                ('tuner_count', models.PositiveIntegerField(default=3)),
            ],
        ),
    ]
