# Generated by Django 5.1.6 on 2025-05-04 21:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('m3u', '0009_m3uaccount_account_type_m3uaccount_password_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='m3uaccount',
            name='last_message',
            field=models.TextField(blank=True, null=True, help_text="Last status message, including success results or error information"),
        ),
        migrations.AddField(
            model_name='m3uaccount',
            name='status',
            field=models.CharField(choices=[('idle', 'Idle'), ('fetching', 'Fetching'), ('parsing', 'Parsing'), ('error', 'Error'), ('success', 'Success')], default='idle', max_length=20),
        ),
        migrations.AlterField(
            model_name='m3uaccount',
            name='updated_at',
            field=models.DateTimeField(blank=True, help_text='Time when this account was last successfully refreshed', null=True),
        ),
    ]
