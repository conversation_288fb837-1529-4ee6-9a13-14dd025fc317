# Generated by Django 5.1.6

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('m3u', '0007_remove_m3uaccount_uploaded_file_m3uaccount_file_path'),
    ]

    operations = [
        migrations.AddField(
            model_name='m3uaccount',
            name='stale_stream_days',
            field=models.PositiveIntegerField(default=7, help_text='Number of days after which a stream will be removed if not seen in the M3U source.'),
        ),
    ]
