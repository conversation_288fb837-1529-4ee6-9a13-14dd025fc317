# Generated by Django 5.1.6 on 2025-03-01 14:01

from django.db import migrations
from core.models import CoreSettings

def create_custom_account(apps, schema_editor):
    default_user_agent_id = CoreSettings.get_default_user_agent_id()

    M3UAccount = apps.get_model("m3u", "M3UAccount")
    m3u_account = M3UAccount.objects.create(
        name="custom",
        max_streams=0,
        is_active=True,
        user_agent_id=default_user_agent_id,
        locked=True,
    )

    M3UAccountProfile = apps.get_model("m3u", "M3UAccountProfile")
    M3UAccountProfile.objects.create(
        m3u_account=m3u_account,
        name=f'{m3u_account.name} Default',
        max_streams=m3u_account.max_streams,
        is_default=True,
        is_active=True,
        search_pattern="^(.*)$",
        replace_pattern="$1",
    )

class Migration(migrations.Migration):

    dependencies = [
        ('m3u', '0002_m3uaccount_locked'),
    ]

    operations = [
        migrations.RunPython(create_custom_account),
    ]
