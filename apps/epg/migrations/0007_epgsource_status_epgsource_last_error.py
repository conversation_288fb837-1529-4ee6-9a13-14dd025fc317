# Generated by Django

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('epg', '0006_epgsource_refresh_interval_epgsource_refresh_task'),
    ]

    operations = [
        migrations.AddField(
            model_name='epgsource',
            name='status',
            field=models.CharField(choices=[('idle', 'Idle'), ('fetching', 'Fetching'), ('parsing', 'Parsing'), ('error', 'Error'), ('success', 'Success')], default='idle', max_length=20),
        ),
        migrations.AddField(
            model_name='epgsource',
            name='last_error',
            field=models.TextField(blank=True, null=True),
        ),
    ]
