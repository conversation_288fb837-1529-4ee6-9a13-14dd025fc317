# Generated by Django 5.1.6 on 2025-04-07 16:29

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('epg', '0007_populate_periodic_tasks'),
    ]

    operations = [
        migrations.AddField(
            model_name='epgsource',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now, help_text='Time when this source was created'),
        ),
        migrations.AddField(
            model_name='epgsource',
            name='updated_at',
            field=models.DateTimeField(default=django.utils.timezone.now, help_text='Time when this source was last updated'),
        ),
    ]
