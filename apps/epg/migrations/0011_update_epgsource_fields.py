# Generated by Django 5.1.6 on 2025-05-04 21:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('epg', '0010_merge_20250503_2147'),
    ]

    operations = [
        # Change updated_at field
        migrations.AlterField(
            model_name='epgsource',
            name='updated_at',
            field=models.DateTimeField(blank=True, help_text='Time when this source was last successfully refreshed', null=True),
        ),

        # Add new last_message field
        migrations.AddField(
            model_name='epgsource',
            name='last_message',
            field=models.TextField(blank=True, help_text='Last status message, including success results or error information', null=True),
        ),

        # Copy data from last_error to last_message
        migrations.RunPython(
            code=lambda apps, schema_editor: apps.get_model('epg', 'EPGSource').objects.all().update(
                last_message=models.F('last_error')
            ),
            reverse_code=lambda apps, schema_editor: apps.get_model('epg', 'EPGSource').objects.all().update(
                last_error=models.F('last_message')
            ),
        ),

        # Remove the old field
        migrations.RemoveField(
            model_name='epgsource',
            name='last_error',
        ),
    ]
