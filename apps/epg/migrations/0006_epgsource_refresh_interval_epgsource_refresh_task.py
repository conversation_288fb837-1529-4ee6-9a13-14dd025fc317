# Generated by Django 5.1.6 on 2025-03-29 17:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('django_celery_beat', '0019_alter_periodictasks_options'),
        ('epg', '0005_programdata_custom_properties_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='epgsource',
            name='refresh_interval',
            field=models.IntegerField(default=24),
        ),
        migrations.AddField(
            model_name='epgsource',
            name='refresh_task',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='django_celery_beat.periodictask'),
        ),
    ]
