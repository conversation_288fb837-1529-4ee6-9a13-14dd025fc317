# Auto detect text files and perform LF normalization
* text=auto

# Explicitly declare text files that should always be normalized
*.py text
*.js text
*.jsx text
*.css text
*.html text
*.md text

# Declare files that will always have specific line endings
*.bat text eol=crlf
*.sh text eol=lf

# Denote all files that are truly binary and should not be modified
*.png binary
*.jpg binary
*.gif binary
*.ico binary
