.tv-guide .guide-program-container .guide-program {
  position: relative;
  left: 2px;
  top: 2px;
  padding: 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  border-radius: 8px;
  background: linear-gradient(to right, #2D2D2F, #1F1F20);
  /* Default background */
  color: #fff;
  transition: all 0.2s ease-out;
}

.tv-guide .guide-program-container .guide-program.live {
  background: linear-gradient(to right, #3BA882, #245043);
}

.tv-guide .guide-program-container .guide-program.live:hover {
  background: linear-gradient(to right, #2E9E80, #206E5E);
}

.tv-guide .guide-program-container .guide-program.not-live:hover {
  background: linear-gradient(to right, #2F3F3A, #1E2926);
}

/* New styles for expanded programs */
.tv-guide .guide-program-container .guide-program.expanded {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  z-index: 26;
  /* Above other programs but below channel logo */
}

.tv-guide .guide-program-container .guide-program.expanded.live {
  background: linear-gradient(to right, #226F5D, #3BA882);
}

.tv-guide .guide-program-container .guide-program.expanded.not-live {
  background: linear-gradient(to right, #2C3F3A, #206E5E);
}

.tv-guide .guide-program-container .guide-program.expanded.past {
  background: linear-gradient(to right, #1F2423, #2F3A37);
}

/* Ensure channel logo is always on top */
.tv-guide .channel-logo {
  z-index: 30;
  position: relative;
  position: sticky !important;
  left: 0;
  z-index: 30;
}

/* Give focus to expanded elements */
.tv-guide .guide-program-container {
  transform-origin: left center;
  transition: all 0.2s ease-out;
}

.tv-guide .guide-program-container.expanded {
  z-index: 25;
}

/* Make sure the main scrolling container establishes the right stacking context */
.tv-guide {
  position: relative;
}
