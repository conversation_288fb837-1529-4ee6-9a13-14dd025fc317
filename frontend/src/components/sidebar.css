.mantine-Stack-root .navlink {
  display: flex;
  flex-direction: row; /* Ensures horizontal layout */
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  padding: 5px 8px !important;
  border-radius: 6px;
  background-color: transparent; /* Default background when not active */
  border: 1px solid transparent;
  transition: all 0.3s ease;
}

/* Active state styles */
.navlink.navlink-active {
  color: #FFFFFF;
  background-color: #245043;
  border: 1px solid #3BA882;
}

/* Hover effect */
.navlink:hover {
  background-color: #2A2F34; /* Gray hover effect when not active */
  border: 1px solid #3D3D42;
}

/* Hover effect for active state */
.navlink.navlink-active:hover {
  background-color: #3A3A40;
  border: 1px solid #3BA882;
}

/* Collapse condition for justifyContent */
.navlink.navlink-collapsed {
  justify-content: center;
}

.navlink:not(.navlink-collapsed) {
  justify-content: flex-start;
}
