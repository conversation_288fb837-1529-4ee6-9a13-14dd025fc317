// prettier.config.js or .prettierrc.js
export default {
  semi: true, // Add semicolons at the end of statements
  singleQuote: true, // Use single quotes instead of double
  tabWidth: 2, // Set the indentation width
  trailingComma: "es5", // Add trailing commas where valid in ES5
  printWidth: 80, // Wrap lines at 80 characters
  bracketSpacing: true, // Add spaces inside object braces
  arrowParens: "always", // Always include parentheses around arrow function parameters
};
