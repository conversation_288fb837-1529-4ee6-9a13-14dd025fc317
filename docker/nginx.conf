proxy_cache_path /app/logo_cache levels=1:2 keys_zone=logo_cache:10m
                 inactive=24h use_temp_path=off;

server {
    listen NGINX_PORT;

    proxy_connect_timeout 75;
    proxy_send_timeout 300;
    proxy_read_timeout 300;
    client_max_body_size 0;  # Allow file uploads up to 128MB

    # Serve Django via uWSGI
    location / {
        include uwsgi_params;
        uwsgi_pass unix:/app/uwsgi.sock;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
    }

    location /assets/ {
        root /app/static;
    }

    location /static/ {
        root /app;
    }

    location /logos/ {
        root /data;
    }

    location /api/logos/(?<logo_id>\d+)/cache/ {
        proxy_pass http://127.0.0.1:5656;
        proxy_cache logo_cache;
        proxy_cache_key "$scheme$request_uri";  # Cache per logo URL
        proxy_cache_valid 200 24h;  # Cache for 24 hours
        proxy_cache_use_stale error timeout updating;  # Serve stale if Django is slow
    }

    location ~ ^/api/channels/logos/(?<logo_id>\d+)/cache/ {
        proxy_pass http://127.0.0.1:5656;
        proxy_cache logo_cache;
        proxy_cache_key "$scheme$request_uri";  # Cache per logo URL
        proxy_cache_valid 200 24h;  # Cache for 24 hours
        proxy_cache_use_stale error timeout updating;  # Serve stale if Django is slow
    }

    # admin disabled when not in dev mode
    location /admin {
        return 301 /login;
    }

    # Route HDHR request to Django
    location /hdhr {
        include uwsgi_params;
        uwsgi_pass unix:/app/uwsgi.sock;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Host $host:$server_port;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;
    }

    # Serve FFmpeg streams efficiently
    location /output/stream/ {
        proxy_pass http://127.0.0.1:5656;
        proxy_buffering off;
        proxy_set_header Connection keep-alive;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
    }

    # WebSockets for real-time communication
    location /ws/ {
        proxy_pass http://127.0.0.1:8001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
    }

    # Route TS proxy requests to the dedicated instance
    location /proxy/ {
        include uwsgi_params;
        uwsgi_pass unix:/app/uwsgi.sock;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        client_max_body_size 0;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
    }
}
