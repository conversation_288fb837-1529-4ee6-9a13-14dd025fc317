[uwsgi]
; exec-before = python manage.py collectstatic --noinput
; exec-before = python manage.py migrate --noinput

; First run Redis availability check script once
exec-pre = python /app/scripts/wait_for_redis.py

; Start Redis first
attach-daemon = redis-server
; Then start other services
attach-daemon = celery -A dispatcharr worker -l debug --concurrency=4
attach-daemon = celery -A dispatcharr beat -l debug
attach-daemon = daphne -b 0.0.0.0 -p 8001 dispatcharr.asgi:application
attach-daemon = cd /app/frontend && npm run dev

# Core settings
chdir = /app
module = dispatcharr.wsgi:application
virtualenv = /dispatcharrpy
master = true
env = DJANGO_SETTINGS_MODULE=dispatcharr.settings
socket = /app/uwsgi.sock
chmod-socket = 777
vacuum = true
die-on-term = true
static-map = /static=/app/static

# Worker management (Optimize for I/O bound tasks)
workers = 4
threads = 2
enable-threads = true

# Optimize for streaming
http = 0.0.0.0:5656
http-keepalive = 1
buffer-size = 65536  # Increase buffer for large payloads
post-buffering = 4096  # Reduce buffering for real-time streaming
http-timeout = 600  # Prevent disconnects from long streams
lazy-apps = true  # Improve memory efficiency

# Async mode (use gevent for high concurrency)
gevent = 100
async = 100

# Performance tuning
thunder-lock = true
log-4xx = true
log-5xx = true
disable-logging = false
