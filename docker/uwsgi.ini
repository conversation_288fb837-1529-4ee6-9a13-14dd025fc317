[uwsgi]
; exec-before = python manage.py collectstatic --noinput
; exec-before = python manage.py migrate --noinput

; First run Redis availability check script once
exec-pre = python /app/scripts/wait_for_redis.py

; Start Redis first
attach-daemon = redis-server
; Then start other services
attach-daemon = celery -A dispatcharr worker -l info --concurrency=4
attach-daemon = celery -A dispatcharr beat -l error
attach-daemon = daphne -b 0.0.0.0 -p 8001 dispatcharr.asgi:application

# Core settings
chdir = /app
module = dispatcharr.wsgi:application
virtualenv = /dispatcharrpy
master = true
env = DJANGO_SETTINGS_MODULE=dispatcharr.settings
socket = /app/uwsgi.sock
chmod-socket = 777
vacuum = true
die-on-term = true
static-map = /static=/app/static

# Worker management
workers = 4

# Optimize for streaming
http = 0.0.0.0:5656
http-keepalive = 1
buffer-size = 65536  # Increase buffer for large payloads
post-buffering = 4096  # Reduce buffering for real-time streaming
http-timeout = 600  # Prevent disconnects from long streams
lazy-apps = true  # Improve memory efficiency

# Async mode (use gevent for high concurrency)
gevent = 400  # Each unused greenlet costs ~2-4KB of memory
# Higher values have minimal performance impact when idle, but provide capacity for traffic spikes
# If memory usage becomes an issue, reduce this value

# Performance tuning
thunder-lock = true
log-4xx = true
log-5xx = true
disable-logging = false
