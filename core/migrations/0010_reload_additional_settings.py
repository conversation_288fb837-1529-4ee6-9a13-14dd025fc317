# Generated by Django 5.1.6 on 2025-03-01 14:01

from django.db import migrations
from django.utils.text import slugify

def preload_core_settings(apps, schema_editor):
    CoreSettings = apps.get_model("core", "CoreSettings")
    CoreSettings.objects.create(
        key=slugify("Preferred Region"),
        name="Preferred Region",
        value="us",
    )

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0009_m3u_hash_settings'),
    ]

    operations = [
        migrations.RunPython(preload_core_settings),
    ]
