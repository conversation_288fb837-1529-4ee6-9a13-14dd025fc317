# Generated by Django 5.1.6 on 2025-03-01 14:01

from django.db import migrations
from django.utils.text import slugify

def preload_core_settings(apps, schema_editor):
    CoreSettings = apps.get_model("core", "CoreSettings")
    CoreSettings.objects.create(
        key=slugify("Auto-Import Mapped Files"),
        name="Auto-Import Mapped Files",
        value=True,
    )

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0011_fix_stream_profiles_and_user_agents'),
    ]

    operations = [
        migrations.RunPython(preload_core_settings),
    ]
