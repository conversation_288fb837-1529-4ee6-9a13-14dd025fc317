# Generated by Django 5.1.6 on 2025-03-14 17:16

from django.db import migrations
from core.models import CoreSettings

def create_proxy_stream_profile(apps, schema_editor):
    default_user_agent_id = CoreSettings.get_default_user_agent_id()

    StreamProfile = apps.get_model("core", "StreamProfile")
    StreamProfile.objects.create(
        profile_name="Proxy",
        command="",
        parameters="",
        locked=True,
        is_active=True,
        user_agent_id=default_user_agent_id,
    )

    StreamProfile.objects.create(
        profile_name="Redirect",
        command="",
        parameters="",
        locked=True,
        is_active=True,
        user_agent_id=default_user_agent_id,
    )

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0006_set_locked_stream_profiles'),
    ]

    operations = [
        migrations.RunPython(create_proxy_stream_profile)
    ]
