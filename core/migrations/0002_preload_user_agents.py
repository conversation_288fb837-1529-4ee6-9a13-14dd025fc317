# Generated by Django 5.1.6 on 2025-03-01 14:01

from django.db import migrations

def preload_user_agent(apps, schema_editor):
    UserAgent = apps.get_model("core", "UserAgent")
    UserAgent.objects.create(
        user_agent_name="TiviMate",
        user_agent="TiviMate/5.16 (Android 12)",
        description="",
        is_active=True,
    )

    UserAgent.objects.create(
        user_agent_name="VLC",
        user_agent="VLC/3.0.21 LibVLC/3.0.21",
        description="",
        is_active=True,
    )

    UserAgent.objects.create(
        user_agent_name="Chrome",
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.3",
        description="",
        is_active=True,
    )

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(preload_user_agent),
    ]
