# Generated by Django 5.1.6 on 2025-03-01 14:01

from django.db import migrations
from django.utils.text import slugify

def preload_core_settings(apps, schema_editor):
    CoreSettings = apps.get_model("core", "CoreSettings")
    CoreSettings.objects.create(
        key=slugify("Default Stream Profile"),
        name="Default Stream Profile",
        value=1,
    )

    CoreSettings.objects.create(
        key=slugify("Default User-Agent"),
        name="Default User-Agent",
        value=1,
    )

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_preload_stream_profiles'),
    ]

    operations = [
        migrations.RunPython(preload_core_settings),
    ]
