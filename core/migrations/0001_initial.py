# Generated by Django 5.1.6 on 2025-03-05 22:07

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CoreSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.<PERSON>r<PERSON><PERSON>(max_length=255, unique=True)),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('value', models.Char<PERSON>ield(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='StreamProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('profile_name', models.CharField(help_text='Name of the stream profile', max_length=255)),
                ('command', models.Char<PERSON>ield(help_text="Command to execute (e.g., 'yt.sh', 'streamlink', or 'vlc')", max_length=255)),
                ('parameters', models.TextField(help_text='Command-line parameters. Use {userAgent} and {streamUrl} as placeholders.')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this profile is active')),
                ('user_agent', models.CharField(blank=True, help_text='Optional user agent to use. If not set, you can fall back to a default.', max_length=512, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserAgent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_agent_name', models.CharField(help_text='The User-Agent name.', max_length=512, unique=True)),
                ('user_agent', models.CharField(help_text='The complete User-Agent string sent by the client.', max_length=512, unique=True)),
                ('description', models.CharField(blank=True, help_text='An optional description of the client or device type.', max_length=255)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this user agent is currently allowed/recognized.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
