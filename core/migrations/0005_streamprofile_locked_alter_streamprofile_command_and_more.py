# Generated by Django 5.1.6 on 2025-03-14 18:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_preload_core_settings'),
    ]

    operations = [
        migrations.AddField(
            model_name='streamprofile',
            name='locked',
            field=models.BooleanField(default=False, help_text="Protected - can't be deleted or modified"),
        ),
        migrations.AlterField(
            model_name='streamprofile',
            name='command',
            field=models.CharField(blank=True, help_text="Command to execute (e.g., 'yt.sh', 'streamlink', or 'vlc')", max_length=255),
        ),
        migrations.AlterField(
            model_name='streamprofile',
            name='parameters',
            field=models.TextField(blank=True, help_text='Command-line parameters. Use {userAgent} and {streamUrl} as placeholders.'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='streamprofile',
            name='user_agent',
            field=models.ForeignKey(blank=True, help_text='Optional user agent to use. If not set, you can fall back to a default.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.useragent'),
        ),
    ]
