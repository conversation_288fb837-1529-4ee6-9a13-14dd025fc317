# Generated by Django 5.1.6 on 2025-04-04

from django.db import migrations

def fix_stream_profiles_and_user_agents(apps, schema_editor):
    # Get the model
    StreamProfile = apps.get_model("core", "StreamProfile")

    streamlink_profile = StreamProfile.objects.get(name="streamlink", locked=True)
    streamlink_profile.parameters = "{streamUrl} --http-header User-Agent={userAgent} best --stdout"
    streamlink_profile.save()

    UserAgent = apps.get_model("core", "UserAgent")
    tivimate = UserAgent.objects.get(name="TiviMate", user_agent="TiviMate/5.16 (Android 12)")
    if tivimate:
        tivimate.user_agent = "TiviMate/5.1.6 (Android 12)"
        tivimate.save()

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0010_reload_additional_settings'),
    ]

    operations = [
        migrations.RunPython(fix_stream_profiles_and_user_agents),
    ]
