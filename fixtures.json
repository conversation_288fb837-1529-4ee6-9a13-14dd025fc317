[{"model": "core.useragent", "fields": {"user_agent_name": "TiviMate", "user_agent": "TiviMate/5.16 (Android 12)", "description": "", "is_active": true, "created_at": "2025-02-28T20:35:14.668Z", "updated_at": "2025-02-28T20:35:14.668Z"}}, {"model": "core.useragent", "fields": {"user_agent_name": "VLC", "user_agent": "VLC/3.0.21 LibVLC/3.0.21", "description": "", "is_active": true, "created_at": "2025-02-28T20:35:14.668Z", "updated_at": "2025-02-28T20:35:14.668Z"}}, {"model": "core.useragent", "fields": {"user_agent_name": "Chrome", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.3", "description": "", "is_active": true, "created_at": "2025-02-28T20:35:14.668Z", "updated_at": "2025-02-28T20:35:14.668Z"}}, {"model": "core.streamprofile", "pk": 1, "fields": {"profile_name": "ffmpeg", "command": "ffmpeg", "parameters": "-i {streamUrl} -c:a copy -c:v copy -f mpegts pipe:1", "is_active": true, "user_agent": "1"}}, {"model": "core.streamprofile", "fields": {"profile_name": "streamlink", "command": "streamlink", "parameters": "{streamUrl} best --stdout", "is_active": true, "user_agent": "1"}}, {"model": "core.coresettings", "fields": {"key": "default-user-agent", "name": "De<PERSON>ult User-Agent", "value": "1"}}, {"model": "core.coresettings", "fields": {"key": "default-stream-profile", "name": "Default Stream Profile", "value": "1"}}, {"model": "core.coresettings", "pk": 3, "fields": {"key": "preferred-region", "name": "Preferred Region", "value": "us"}}, {"model": "core.coresettings", "fields": {"key": "cache-images", "name": "<PERSON><PERSON>", "value": "true"}}]