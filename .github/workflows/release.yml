name: Create Release

on:
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Type of version increment'
        required: true
        default: 'patch'
        type: choice
        options:
          - major
          - minor
          - patch

# Add explicit permissions for the workflow
permissions:
  contents: write    # For managing releases and pushing tags
  packages: write    # For publishing to GitHub Container Registry

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Configure Git
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

      - name: Update Version
        id: update_version
        run: |
          python scripts/bump_version.py ${{ github.event.inputs.version_type }}
          NEW_VERSION=$(python -c "import version; print(f'{version.__version__}')")
          echo "new_version=${NEW_VERSION}" >> $GITHUB_OUTPUT

      - name: Set lowercase repo owner
        id: repo_owner
        run: |
          REPO_OWNER=$(echo "${{ github.repository_owner }}" | tr '[:upper:]' '[:lower:]')
          echo "lowercase=${REPO_OWNER}" >> $GITHUB_OUTPUT

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Commit and Tag
        run: |
          git add version.py
          git commit -m "Release v${{ steps.update_version.outputs.new_version }}"
          git tag -a "v${{ steps.update_version.outputs.new_version }}" -m "Release v${{ steps.update_version.outputs.new_version }}"
          git push origin main --tags

      - name: Build and Push Release Image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          platforms: linux/amd64,linux/arm64, #linux/arm/v7  # Multi-arch support for releases
          tags: |
            ghcr.io/${{ steps.repo_owner.outputs.lowercase }}/dispatcharr:latest
            ghcr.io/${{ steps.repo_owner.outputs.lowercase }}/dispatcharr:${{ steps.update_version.outputs.new_version }}
            ghcr.io/${{ steps.repo_owner.outputs.lowercase }}/dispatcharr:latest-amd64
            ghcr.io/${{ steps.repo_owner.outputs.lowercase }}/dispatcharr:latest-arm64
            ghcr.io/${{ steps.repo_owner.outputs.lowercase }}/dispatcharr:${{ steps.update_version.outputs.new_version }}-amd64
            ghcr.io/${{ steps.repo_owner.outputs.lowercase }}/dispatcharr:${{ steps.update_version.outputs.new_version }}-arm64
          build-args: |
            BRANCH=${{ github.ref_name }}
            REPO_URL=https://github.com/${{ github.repository }}
          file: ./docker/Dockerfile

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ steps.update_version.outputs.new_version }}
          name: Release v${{ steps.update_version.outputs.new_version }}
          draft: false
          prerelease: false
          token: ${{ secrets.GITHUB_TOKEN }}
