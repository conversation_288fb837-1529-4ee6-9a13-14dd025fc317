name: CI Pipeline

on:
  push:
    branches: [ dev ]
  pull_request:
    branches: [ dev ]
  workflow_dispatch:  # Allow manual triggering

# Add explicit permissions for the workflow
permissions:
  contents: write    # For managing releases and pushing tags
  packages: write    # For publishing to GitHub Container Registry

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Configure Git
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

      - name: Check if commit is from GitHub Actions
        id: check_actor
        run: |
          if [[ "${{ github.actor }}" == "github-actions" ]]; then
            echo "is_bot=true" >> $GITHUB_OUTPUT
          else
            echo "is_bot=false" >> $GITHUB_OUTPUT
          fi

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Generate timestamp for build
        id: timestamp
        run: |
          TIMES<PERSON>MP=$(date -u +'%Y%m%d%H%M%S')
          echo "timestamp=${TIMESTAMP}" >> $GITHUB_OUTPUT

      - name: Extract version info
        id: version
        run: |
          VERSION=$(python -c "import version; print(version.__version__)")
          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          echo "sha_short=${GITHUB_SHA::7}" >> $GITHUB_OUTPUT

      - name: Set repository and image metadata
        id: meta
        run: |
          # Get lowercase repository owner
          REPO_OWNER=$(echo "${{ github.repository_owner }}" | tr '[:upper:]' '[:lower:]')
          echo "repo_owner=${REPO_OWNER}" >> $GITHUB_OUTPUT

          # Get repository name
          REPO_NAME=$(echo "${{ github.repository }}" | cut -d '/' -f 2 | tr '[:upper:]' '[:lower:]')
          echo "repo_name=${REPO_NAME}" >> $GITHUB_OUTPUT

          # Determine branch name
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "branch_tag=latest" >> $GITHUB_OUTPUT
            echo "is_main=true" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/dev" ]]; then
            echo "branch_tag=dev" >> $GITHUB_OUTPUT
            echo "is_main=false" >> $GITHUB_OUTPUT
          else
            # For other branches, use the branch name
            BRANCH=$(echo "${{ github.ref }}" | sed 's/refs\/heads\///' | sed 's/[^a-zA-Z0-9]/-/g')
            echo "branch_tag=${BRANCH}" >> $GITHUB_OUTPUT
            echo "is_main=false" >> $GITHUB_OUTPUT
          fi

          # Determine if this is from a fork
          if [[ "${{ github.event.pull_request.head.repo.fork }}" == "true" ]]; then
            echo "is_fork=true" >> $GITHUB_OUTPUT
          else
            echo "is_fork=false" >> $GITHUB_OUTPUT
          fi

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          platforms: linux/amd64,linux/arm64
          tags: |
            ghcr.io/${{ steps.meta.outputs.repo_owner }}/${{ steps.meta.outputs.repo_name }}:${{ steps.meta.outputs.branch_tag }}
            ghcr.io/${{ steps.meta.outputs.repo_owner }}/${{ steps.meta.outputs.repo_name }}:${{ steps.version.outputs.version }}-${{ steps.timestamp.outputs.timestamp }}
            ghcr.io/${{ steps.meta.outputs.repo_owner }}/${{ steps.meta.outputs.repo_name }}:${{ steps.version.outputs.sha_short }}
          build-args: |
            REPO_OWNER=${{ steps.meta.outputs.repo_owner }}
            REPO_NAME=${{ steps.meta.outputs.repo_name }}
            BASE_TAG=base
            BRANCH=${{ github.ref_name }}
            REPO_URL=https://github.com/${{ github.repository }}
            TIMESTAMP=${{ steps.timestamp.outputs.timestamp }}
          file: ./docker/Dockerfile
